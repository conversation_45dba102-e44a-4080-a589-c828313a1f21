ZigZag转折点交易EA详细策略分析
核心策略原理
这个EA基于ZigZag指标的转折点分析和斐波那契回调理论进行自动交易。

1. 基础分析机制
ZigZag转折点识别：

使用ZigZag指标找到最近的3个转折点（z1、z2、z3）
z1是最新的转折点，z2是第二个，z3是第三个
从第4根K线开始查找转折点（可配置）
趋势判断：

计算d1 = |z1 - z2|（最近两个转折点的差值）
计算d2 = |z2 - z3|（第二和第三个转折点的差值）
d = max(d1, d2)（取较大的差值作为主要波动幅度）
根据d的来源确定趋势方向：
假设z3<z2,z2>z1，
如果d来自d1：以z2为起点，z1为终点，为下跌趋势；
如果d来自d2：以z3为起点，z2为终点，为上涨趋势；
2. 斐波那契回调系统
档位设置（8个档位）：

第1档：0.0（起点）
第2档：0.236（黄金分割）
第3档：0.382
第4档：0.5（中点）
第5档：0.618（黄金分割）
第6档：0.786
第7档：0.876
第8档：1.0（终点）

价格计算：

上升趋势：回调价格 = 终点价格 - d × 斐波那契比例
下降趋势：回调价格 = 终点价格 + d × 斐波那契比例

3. 智能入场策略
初始下单档位判断（基于d值大小）：

d > 40：从第2档开始
30 < d ≤ 40：从第3档开始
20 < d ≤ 30：从第4档开始
15 < d ≤ 20：从第5档开始（极低d值）
5 < d ≤ 15：从第6档开始（超低d值）
d ≤ 5：不交易

3.下单条件：

看涨趋势时，上上上柱或上上柱最低点价格小于对应的斐波那契档位价格，上上柱为阳线，上柱为阳线，上上上柱为阴线，下多单，止损价为前4柱最低点；
看跌趋势时，上上上柱或上上柱最高点价格大于对应的斐波那契档位价格，上上柱为阴线，上柱为阴线，上上上柱为阳线，下空单，止损价为前4柱最高点。


4.提前入场功能：

在价格完全回调到位之前3%入场
提高入场成功率


5.持仓保护：

持仓期间不更新ZigZag转折点
防止持仓中途改变交易逻辑

平仓后处理：

平仓后更新ZigZag转折点

6.移动止损功能：
增加移动止损功能，当开启时，当新的k线出现时，多单检测上一柱最低点是否盈利1*0.1*d*100*point，空单检测上一柱最高点是否盈利1*0.1*d*100*point，
若是，则修改止损为上一柱最低点-44*point（多单）和上一柱最高点+44*point（空单）。1可以设置。

7.补充要求：
数字设置可以修改的都要方便我后期回测修改。